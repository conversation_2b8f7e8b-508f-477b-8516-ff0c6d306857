#!/usr/bin/env python3
"""
调试当前LinkedIn页面状态
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from loguru import logger

def debug_current_page():
    """调试当前LinkedIn页面状态"""
    
    print("🔍 LinkedIn页面状态调试工具")
    print("=" * 50)
    
    # 配置日志
    logger.add("log/debug_current_state.log", rotation="1 MB", level="DEBUG")
    
    try:
        # 连接到现有的Chrome实例
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("✅ 成功连接到现有Chrome实例")
        except Exception as e:
            print(f"❌ 无法连接到现有Chrome实例: {str(e)}")
            print("请确保Chrome浏览器正在运行并且测试正在进行中")
            return
        
        # 获取当前页面信息
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"\n📄 当前页面信息:")
        print(f"URL: {current_url}")
        print(f"标题: {page_title}")
        
        # 检查是否在LinkedIn域名
        if "linkedin.com" not in current_url:
            print("⚠️  当前不在LinkedIn页面")
            return
        
        # 查找所有按钮
        print(f"\n🔘 页面按钮分析:")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        visible_buttons = [btn for btn in all_buttons if btn.is_displayed()]
        enabled_buttons = [btn for btn in visible_buttons if btn.is_enabled()]
        
        print(f"总按钮数: {len(all_buttons)}")
        print(f"可见按钮数: {len(visible_buttons)}")
        print(f"可点击按钮数: {len(enabled_buttons)}")
        
        # 显示前10个可点击按钮的详细信息
        print(f"\n📋 前10个可点击按钮:")
        for i, btn in enumerate(enabled_buttons[:10]):
            btn_text = btn.text.strip() or btn.get_attribute('aria-label') or f'Button {i+1}'
            btn_class = btn.get_attribute('class') or 'no-class'
            btn_id = btn.get_attribute('id') or 'no-id'
            print(f"  {i+1}. '{btn_text}' (id: {btn_id}, class: {btn_class[:50]})")
        
        # 查找申请相关元素
        print(f"\n🎯 申请相关元素:")
        
        # Easy Apply按钮
        easy_apply_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Easy Apply') or contains(@aria-label, 'Easy Apply')]")
        if easy_apply_buttons:
            print(f"找到 {len(easy_apply_buttons)} 个Easy Apply按钮")
            for i, btn in enumerate(easy_apply_buttons):
                print(f"  Easy Apply {i+1}: {btn.text} (可见: {btn.is_displayed()}, 可点击: {btn.is_enabled()})")
        
        # Next按钮
        next_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Next') or contains(@aria-label, 'Next')]")
        if next_buttons:
            print(f"找到 {len(next_buttons)} 个Next按钮")
            for i, btn in enumerate(next_buttons):
                print(f"  Next {i+1}: {btn.text} (可见: {btn.is_displayed()}, 可点击: {btn.is_enabled()})")
        
        # Submit按钮
        submit_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Submit') or contains(@aria-label, 'Submit')]")
        if submit_buttons:
            print(f"找到 {len(submit_buttons)} 个Submit按钮")
            for i, btn in enumerate(submit_buttons):
                print(f"  Submit {i+1}: {btn.text} (可见: {btn.is_displayed()}, 可点击: {btn.is_enabled()})")
        
        # Review按钮
        review_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Review') or contains(@aria-label, 'Review')]")
        if review_buttons:
            print(f"找到 {len(review_buttons)} 个Review按钮")
            for i, btn in enumerate(review_buttons):
                print(f"  Review {i+1}: {btn.text} (可见: {btn.is_displayed()}, 可点击: {btn.is_enabled()})")
        
        # 查找模态框
        print(f"\n🪟 模态框检测:")
        modals = driver.find_elements(By.CSS_SELECTOR, ".artdeco-modal, .jobs-easy-apply-modal, [data-test-modal]")
        if modals:
            print(f"找到 {len(modals)} 个模态框")
            for i, modal in enumerate(modals):
                modal_class = modal.get_attribute('class')
                print(f"  模态框 {i+1}: {modal_class} (可见: {modal.is_displayed()})")
        else:
            print("未找到模态框")
        
        # 查找表单元素
        print(f"\n📝 表单元素:")
        inputs = driver.find_elements(By.CSS_SELECTOR, "input, textarea, select")
        visible_inputs = [inp for inp in inputs if inp.is_displayed()]
        print(f"找到 {len(visible_inputs)} 个可见的表单元素")
        
        for i, inp in enumerate(visible_inputs[:5]):  # 只显示前5个
            inp_type = inp.get_attribute('type') or inp.tag_name
            inp_placeholder = inp.get_attribute('placeholder') or 'no-placeholder'
            inp_name = inp.get_attribute('name') or 'no-name'
            print(f"  {i+1}. {inp_type} (name: {inp_name}, placeholder: {inp_placeholder})")
        
        # 保存当前页面截图
        try:
            screenshot_path = f"log/debug_current_state_{int(time.time())}.png"
            driver.save_screenshot(screenshot_path)
            print(f"\n📸 页面截图已保存: {screenshot_path}")
        except Exception as e:
            print(f"❌ 保存截图失败: {str(e)}")
        
        # 保存页面HTML
        try:
            html_path = f"log/debug_current_state_{int(time.time())}.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            print(f"📄 页面HTML已保存: {html_path}")
        except Exception as e:
            print(f"❌ 保存HTML失败: {str(e)}")
        
        print(f"\n" + "=" * 50)
        print("调试信息收集完成！")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {str(e)}")

def main():
    """主函数"""
    debug_current_page()

if __name__ == "__main__":
    main()
