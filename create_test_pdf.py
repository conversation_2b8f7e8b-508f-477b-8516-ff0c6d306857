from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_test_resume_pdf():
    # 创建PDF文件
    filename = "test_resume.pdf"
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # 设置字体（使用默认字体，避免中文字体问题）
    c.setFont("Helvetica-Bold", 16)
    
    # 添加内容
    y_position = height - 50
    
    # 标题
    c.drawString(50, y_position, "Zhang San")
    y_position -= 30
    c.setFont("Helvetica", 14)
    c.drawString(50, y_position, "Software Engineer")
    y_position -= 40
    
    # 联系方式
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, y_position, "Contact Information:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(50, y_position, "Phone: 138-0000-0000")
    y_position -= 15
    c.drawString(50, y_position, "Email: zhang<PERSON>@email.com")
    y_position -= 15
    c.drawString(50, y_position, "Address: Beijing, Chaoyang District")
    y_position -= 30
    
    # 教育背景
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, y_position, "Education:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(50, y_position, "2018-2022 Peking University")
    y_position -= 15
    c.drawString(50, y_position, "Bachelor of Computer Science and Technology")
    y_position -= 30
    
    # 工作经验
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, y_position, "Work Experience:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(50, y_position, "2022-2024 Tencent Technology - Frontend Engineer")
    y_position -= 15
    c.drawString(50, y_position, "- Responsible for WeChat Mini Program development")
    y_position -= 15
    c.drawString(50, y_position, "- Developed Web applications using React and Vue.js")
    y_position -= 15
    c.drawString(50, y_position, "- Participated in team code reviews and technical sharing")
    y_position -= 30
    
    # 技能
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, y_position, "Skills:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(50, y_position, "Programming Languages: JavaScript, Python, Java")
    y_position -= 15
    c.drawString(50, y_position, "Frontend Frameworks: React, Vue.js, Angular")
    y_position -= 15
    c.drawString(50, y_position, "Backend Technologies: Node.js, Express")
    y_position -= 15
    c.drawString(50, y_position, "Databases: MySQL, MongoDB")
    y_position -= 15
    c.drawString(50, y_position, "Tools: Git, Docker, Jenkins")
    y_position -= 30
    
    # 项目经验
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, y_position, "Project Experience:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(50, y_position, "1. E-commerce Platform Frontend Development")
    y_position -= 15
    c.drawString(70, y_position, "- Developed responsive e-commerce website using React")
    y_position -= 15
    c.drawString(70, y_position, "- Integrated Alipay and WeChat Pay")
    y_position -= 15
    c.drawString(70, y_position, "- Optimized page loading speed and user experience")
    y_position -= 20
    
    c.drawString(50, y_position, "2. Enterprise Management System")
    y_position -= 15
    c.drawString(70, y_position, "- Developed internal management system using Vue.js")
    y_position -= 15
    c.drawString(70, y_position, "- Implemented permission management and data visualization")
    y_position -= 15
    c.drawString(70, y_position, "- Supported multi-language internationalization")
    
    # 保存PDF
    c.save()
    print(f"Test resume PDF created: {filename}")
    return filename

if __name__ == "__main__":
    create_test_resume_pdf()
