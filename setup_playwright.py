#!/usr/bin/env python3
"""
Playwright 浏览器安装脚本
确保 Playwright 浏览器正确安装
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*50}")
    print(f"正在{description}...")
    print(f"命令: {command}")
    print('='*50)
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ 成功!")
        if result.stdout:
            print("输出:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False

def check_playwright_installation():
    """检查 Playwright 是否已安装"""
    try:
        import playwright
        print("✅ Playwright 包已安装")
        return True
    except ImportError:
        print("❌ Playwright 包未安装")
        return False

def install_playwright():
    """安装 Playwright 和浏览器"""
    print("Playwright 安装程序")
    print("="*50)
    
    # 检查 Python 版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    # 检查是否已安装 Playwright 包
    if not check_playwright_installation():
        print("\n安装 Playwright 包...")
        if not run_command(f"{sys.executable} -m pip install playwright>=1.40.0", "安装 Playwright 包"):
            print("❌ Playwright 包安装失败")
            return False
    
    # 安装 Playwright 浏览器
    print("\n安装 Playwright 浏览器...")
    if not run_command(f"{sys.executable} -m playwright install chromium", "安装 Chromium 浏览器"):
        print("❌ Playwright 浏览器安装失败")
        return False
    
    # 验证安装
    print("\n验证安装...")
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://www.google.com")
            browser.close()
        print("✅ Playwright 浏览器测试成功!")
    except Exception as e:
        print(f"❌ Playwright 浏览器测试失败: {e}")
        return False
    
    print("\n🎉 Playwright 安装完成!")
    print("\n现在您可以使用 Playwright 作为默认的浏览器自动化工具。")
    
    return True

if __name__ == "__main__":
    success = install_playwright()
    if not success:
        print("\n❌ 安装失败，请检查错误信息并重试。")
        sys.exit(1)
    else:
        print("\n✅ 安装成功!")
        sys.exit(0)
