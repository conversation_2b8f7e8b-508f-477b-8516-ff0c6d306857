#!/usr/bin/env python3
"""
LinkedIn Review按钮调试脚本
专门用于调试申请流程中Review按钮卡住的问题
"""

import sys
import os
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_review_button.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def debug_application_buttons(driver, wait):
    """调试申请流程中的按钮"""
    logger.info("开始调试申请流程按钮...")
    
    try:
        # 等待页面加载
        time.sleep(3)
        
        # 查找所有可见的按钮
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        visible_buttons = [btn for btn in all_buttons if btn.is_displayed()]
        
        logger.info(f"找到 {len(visible_buttons)} 个可见按钮:")
        
        for i, btn in enumerate(visible_buttons):
            try:
                btn_text = btn.text.strip()
                btn_aria_label = btn.get_attribute('aria-label') or ''
                btn_class = btn.get_attribute('class') or ''
                btn_enabled = btn.is_enabled()
                
                logger.info(f"  按钮 {i+1}:")
                logger.info(f"    文本: '{btn_text}'")
                logger.info(f"    aria-label: '{btn_aria_label}'")
                logger.info(f"    class: '{btn_class[:100]}...'")
                logger.info(f"    是否可点击: {btn_enabled}")
                
                # 特别关注包含Next、Review、Submit的按钮
                if any(keyword in btn_text.lower() for keyword in ['next', 'review', 'submit', '下一步', '审核', '提交']):
                    logger.info(f"    *** 重要按钮: {btn_text} ***")
                    
                    if btn_enabled:
                        logger.info(f"    尝试点击按钮: {btn_text}")
                        btn.click()
                        time.sleep(3)
                        
                        # 检查页面是否有变化
                        logger.info("    按钮点击后，重新检查页面...")
                        return debug_application_buttons(driver, wait)
                
            except Exception as e:
                logger.warning(f"  按钮 {i+1} 检查失败: {str(e)}")
        
        # 检查是否有成功消息
        try:
            success_selectors = [
                "//*[contains(text(), 'Application sent')]",
                "//*[contains(text(), '申请已发送')]",
                "//*[contains(text(), 'Your application was sent')]",
                "//*[contains(text(), 'Thank you')]"
            ]
            
            for selector in success_selectors:
                try:
                    success_element = driver.find_element(By.XPATH, selector)
                    if success_element.is_displayed():
                        logger.info(f"找到成功消息: {success_element.text}")
                        return True
                except NoSuchElementException:
                    continue
                    
        except Exception as e:
            logger.warning(f"检查成功消息失败: {str(e)}")
        
        return False
        
    except Exception as e:
        logger.error(f"调试按钮失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("LinkedIn Review按钮调试开始...")
    
    # 设置Chrome选项
    from selenium.webdriver.chrome.options import Options
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = None
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.set_window_size(1920, 1080)
        
        wait = WebDriverWait(driver, 10)
        
        # 打开LinkedIn
        logger.info("打开LinkedIn...")
        driver.get("https://www.linkedin.com")
        time.sleep(3)
        
        logger.info("请手动登录LinkedIn并导航到一个Easy Apply职位页面...")
        logger.info("然后点击Easy Apply按钮，进入申请流程...")
        logger.info("当到达Review步骤时，按Enter键开始调试...")
        
        input("按Enter键开始调试申请流程...")
        
        # 开始调试
        debug_application_buttons(driver, wait)
        
        logger.info("调试完成，按Enter键退出...")
        input()
        
    except Exception as e:
        logger.error(f"调试过程出错: {str(e)}")
    finally:
        if driver:
            driver.quit()
            logger.info("浏览器已关闭")

if __name__ == "__main__":
    main()
