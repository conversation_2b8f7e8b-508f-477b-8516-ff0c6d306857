import React, { createContext, useContext, useState, useEffect } from 'react';

// 设置上下文
const SettingsContext = createContext();

// 默认设置配置
const defaultSettings = {
  // LinkedIn自动化设置
  linkedin: {
    automation_type: 'selenium', // selenium, playwright, playwright_async
    browser: {
      headless: false,
      timeout: 30000,
      retry_count: 3,
      window_size: [1920, 1080]
    },
    application: {
      max_applications_per_day: 50,
      delay_between_applications: [30, 60],
      auto_answer_questions: true,
      default_answers: {
        years_experience: '3',
        willing_to_relocate: 'Yes',
        authorized_to_work: 'Yes',
        require_sponsorship: 'No'
      }
    },
    templates: {
      cover_letter_template: '',
      follow_up_message: '',
      thank_you_message: ''
    }
  },
  
  // 数据管理设置
  data: {
    auto_backup: true,
    backup_frequency: 'daily', // daily, weekly, monthly
    max_backup_files: 10,
    export_format: 'json', // json, csv, yaml
    clear_history_days: 30
  },
  
  // 系统设置
  system: {
    theme: 'dark', // dark, light
    language: 'zh', // zh, en
    font_size: 'medium', // small, medium, large
    notifications: {
      desktop: true,
      sound: true,
      email: false
    },
    auto_save: true,
    debug_mode: false
  },
  
  // 高级功能设置
  advanced: {
    ai: {
      model_type: 'gemini',
      model_name: 'gemini-2.5-flash',
      temperature: 1.0,
      creativity_level: 'balanced', // conservative, balanced, creative
      optimization_strength: 'medium' // light, medium, strong
    },
    batch: {
      max_concurrent_jobs: 3,
      batch_size: 10,
      auto_retry_failed: true,
      retry_delay: 5000
    },
    performance: {
      cache_enabled: true,
      cache_duration: 3600000, // 1 hour in ms
      preload_data: true,
      lazy_loading: true
    }
  }
};

// 设置提供者组件
export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  // 从localStorage恢复设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // 从localStorage加载设置
        const savedSettings = localStorage.getItem('userSettings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          setSettings(prevSettings => ({
            ...prevSettings,
            ...parsedSettings
          }));
        }

        // 尝试从后端加载设置
        try {
          const response = await fetch('http://localhost:8003/api/settings/load');
          if (response.ok) {
            const serverSettings = await response.json();
            setSettings(prevSettings => ({
              ...prevSettings,
              ...serverSettings
            }));
          }
        } catch (error) {
          console.warn('无法从服务器加载设置，使用本地设置:', error);
        }
      } catch (error) {
        console.error('加载设置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // 保存设置到localStorage
  const saveToLocalStorage = (newSettings) => {
    try {
      localStorage.setItem('userSettings', JSON.stringify(newSettings));
    } catch (error) {
      console.error('保存设置到localStorage失败:', error);
    }
  };

  // 保存设置到服务器
  const saveToServer = async (newSettings) => {
    try {
      const response = await fetch('http://localhost:8003/api/settings/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });
      
      if (!response.ok) {
        throw new Error('保存设置到服务器失败');
      }
      
      return true;
    } catch (error) {
      console.error('保存设置到服务器失败:', error);
      return false;
    }
  };

  // 更新设置
  const updateSettings = async (path, value) => {
    const newSettings = { ...settings };
    
    // 使用路径更新嵌套对象
    const keys = path.split('.');
    let current = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    
    setSettings(newSettings);
    setHasChanges(true);
    
    // 立即保存到localStorage
    saveToLocalStorage(newSettings);
    
    // 如果启用了自动保存，也保存到服务器
    if (newSettings.system?.auto_save) {
      await saveToServer(newSettings);
      setHasChanges(false);
    }
  };

  // 批量更新设置
  const updateMultipleSettings = async (updates) => {
    let newSettings = { ...settings };
    
    updates.forEach(({ path, value }) => {
      const keys = path.split('.');
      let current = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
    });
    
    setSettings(newSettings);
    setHasChanges(true);
    
    saveToLocalStorage(newSettings);
    
    if (newSettings.system?.auto_save) {
      await saveToServer(newSettings);
      setHasChanges(false);
    }
  };

  // 手动保存设置
  const saveSettings = async () => {
    const success = await saveToServer(settings);
    if (success) {
      setHasChanges(false);
    }
    return success;
  };

  // 重置设置
  const resetSettings = async (section = null) => {
    let newSettings;
    
    if (section) {
      newSettings = {
        ...settings,
        [section]: defaultSettings[section]
      };
    } else {
      newSettings = { ...defaultSettings };
    }
    
    setSettings(newSettings);
    saveToLocalStorage(newSettings);
    
    if (newSettings.system?.auto_save) {
      await saveToServer(newSettings);
      setHasChanges(false);
    } else {
      setHasChanges(true);
    }
  };

  // 获取设置值
  const getSetting = (path) => {
    const keys = path.split('.');
    let current = settings;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  };

  const value = {
    settings,
    isLoading,
    hasChanges,
    updateSettings,
    updateMultipleSettings,
    saveSettings,
    resetSettings,
    getSetting
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

// 使用设置的Hook
export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export default SettingsContext;
