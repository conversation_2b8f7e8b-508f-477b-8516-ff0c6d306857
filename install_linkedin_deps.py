#!/usr/bin/env python3
"""
LinkedIn自动化依赖安装脚本
自动安装所需的Python包和Playwright浏览器
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"正在{description}...")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ 成功!")
        if result.stdout:
            print("输出:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False

def install_linkedin_dependencies():
    """安装LinkedIn自动化所需的依赖"""
    print("LinkedIn自动化依赖安装程序")
    print("="*60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        print("⚠️ 警告: pip升级失败，继续安装...")
    
    # 核心依赖列表
    core_dependencies = [
        "playwright>=1.40.0",
        "beautifulsoup4>=4.12.0",
        "loguru>=0.7.2",
        "PyYAML>=6.0.1",
        "fastapi>=0.104.0",
        "uvicorn>=0.24.0",
        "pydantic>=2.5.0",
        "httpx>=0.25.0",
        "selenium>=4.15.0",
        "webdriver-manager>=4.0.1"
    ]
    
    # 安装核心依赖
    for dep in core_dependencies:
        if not run_command(f"{sys.executable} -m pip install {dep}", f"安装 {dep}"):
            print(f"❌ 安装 {dep} 失败")
            return False
    
    # 安装Playwright浏览器
    print("\n" + "="*60)
    print("安装Playwright浏览器...")
    print("="*60)
    
    if not run_command(f"{sys.executable} -m playwright install chromium", "安装Chromium浏览器"):
        print("❌ Playwright浏览器安装失败")
        return False
    
    # 可选依赖（如果安装失败不会终止程序）
    optional_dependencies = [
        "aiofiles>=23.2.0",
        "pandas>=2.1.0",
        "numpy>=1.24.0",
        "python-dateutil>=2.8.2",
        "python-dotenv>=1.0.0",
        "requests>=2.31.0"
    ]
    
    print("\n" + "="*60)
    print("安装可选依赖...")
    print("="*60)
    
    for dep in optional_dependencies:
        if not run_command(f"{sys.executable} -m pip install {dep}", f"安装 {dep}"):
            print(f"⚠️ 警告: 安装 {dep} 失败，但不影响核心功能")
    
    print("\n" + "="*60)
    print("✅ 依赖安装完成!")
    print("="*60)
    
    # 验证安装
    print("\n验证安装...")
    try:
        import playwright
        print("✅ Playwright 安装成功")
    except ImportError:
        print("❌ Playwright 安装失败")
        return False
    
    try:
        import bs4
        print("✅ BeautifulSoup4 安装成功")
    except ImportError:
        print("❌ BeautifulSoup4 安装失败")
        return False
    
    try:
        from loguru import logger
        print("✅ Loguru 安装成功")
    except ImportError:
        print("❌ Loguru 安装失败")
        return False
    
    print("\n🎉 所有核心依赖安装成功!")
    print("\n下一步:")
    print("1. 运行 'python test_linkedin_automation.py' 测试功能")
    print("2. 在 test_linkedin_config.yaml 中配置您的LinkedIn账号")
    print("3. 启动Web UI: 'cd webui/backend && python main.py'")
    
    return True

def check_existing_installation():
    """检查现有安装"""
    print("检查现有安装...")
    
    packages_to_check = [
        ("playwright", "Playwright"),
        ("bs4", "BeautifulSoup4"),
        ("loguru", "Loguru"),
        ("yaml", "PyYAML"),
        ("fastapi", "FastAPI"),
        ("selenium", "Selenium")
    ]
    
    installed = []
    missing = []
    
    for package, name in packages_to_check:
        try:
            __import__(package)
            installed.append(name)
        except ImportError:
            missing.append(name)
    
    if installed:
        print(f"✅ 已安装: {', '.join(installed)}")
    
    if missing:
        print(f"❌ 缺失: {', '.join(missing)}")
        return False
    else:
        print("✅ 所有依赖都已安装!")
        return True

if __name__ == "__main__":
    print("LinkedIn自动化依赖检查和安装")
    print("="*60)
    
    # 检查现有安装
    if check_existing_installation():
        response = input("\n所有依赖都已安装。是否重新安装？(y/N): ").strip().lower()
        if response != 'y':
            print("跳过安装")
            sys.exit(0)
    
    # 安装依赖
    if install_linkedin_dependencies():
        print("\n🎉 安装完成!")
    else:
        print("\n❌ 安装失败，请检查错误信息")
        sys.exit(1)
