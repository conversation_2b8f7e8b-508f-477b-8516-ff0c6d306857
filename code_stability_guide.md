# 代码稳定性保障指南

## 当前代码状态快照
- 日期: 2025-06-20
- 主要功能: 求职信生成、简历优化、LinkedIn自动化
- 最新修改: 求职信对齐和分割线优化

## 代码锁定措施

### 1. 创建稳定版本标签
```bash
cd "d:\Jobs_Applier_AI_Agent_AIHawk-main"
git tag -a v1.0-stable-20250620 -m "稳定版本 - 求职信功能优化完成"
git push origin v1.0-stable-20250620
```

### 2. 备份当前代码
```bash
# 创建备份目录
mkdir "d:\Jobs_Applier_AI_Agent_AIHawk-backup-20250620"

# 复制整个项目
xcopy "d:\Jobs_Applier_AI_Agent_AIHawk-main" "d:\Jobs_Applier_AI_Agent_AIHawk-backup-20250620" /E /I /H
```

### 3. 锁定依赖版本
确保以下文件存在并包含固定版本：

#### requirements.txt 检查
- 确保所有包都有明确版本号
- 避免使用 `>=` 或 `~` 等动态版本

#### package.json 检查（如果有前端）
- 使用 `npm shrinkwrap` 或 `yarn.lock` 锁定版本

### 4. 环境配置锁定

#### Python虚拟环境
```bash
# 如果使用虚拟环境，导出当前环境
pip freeze > requirements_locked.txt
```

#### 配置文件备份
- 备份所有 `.env` 文件
- 备份配置文件 `config.py` 或类似文件

## 下次启动检查清单

### 1. 环境一致性检查
- [ ] Python版本一致
- [ ] 依赖包版本一致
- [ ] 环境变量设置正确

### 2. 功能验证
- [ ] 求职信生成功能正常
- [ ] 简历优化功能正常
- [ ] LinkedIn自动化功能正常
- [ ] 文件上传功能正常

### 3. 关键文件检查
- [ ] `webui/backend/main.py` - 求职信样式修改完整
- [ ] 所有CSS样式正确应用
- [ ] 数据库连接正常（如果有）

## 恢复操作（如果出现问题）

### 从备份恢复
```bash
xcopy "d:\Jobs_Applier_AI_Agent_AIHawk-backup-20250620" "d:\Jobs_Applier_AI_Agent_AIHawk-main" /E /I /H /Y
```

### 从Git标签恢复
```bash
git checkout v1.0-stable-20250620
```

## 预防措施
1. 定期创建代码快照
2. 重要修改前先创建分支
3. 保持详细的修改日志
4. 测试环境与生产环境分离
