# AIHawk 设置功能说明

## 概述

AIHawk 设置功能提供了全面的配置管理系统，允许用户自定义LinkedIn自动化、数据管理、系统设置和高级功能参数。

## 功能特性

### 1. LinkedIn自动化设置
- **自动化模式选择**: Selenium、Playwright同步/异步
- **浏览器配置**: 无头模式、超时时间、重试次数、窗口大小
- **申请频率限制**: 每日最大申请数、申请间隔时间
- **自动回复模板**: 工作经验、搬迁意愿、工作授权等

### 2. 数据管理
- **备份管理**: 自动备份、手动备份、备份恢复
- **数据导出/导入**: JSON、YAML、CSV格式支持
- **历史记录管理**: 自动清理、手动清除
- **缓存管理**: 缓存清理、存储优化

### 3. 系统设置
- **界面主题**: 深色/浅色主题切换
- **语言偏好**: 中文/英文界面（保存偏好）
- **字体设置**: 小/中/大字体大小
- **通知设置**: 桌面通知、声音提醒、邮件通知

### 4. 高级功能设置
- **AI优化参数**: 模型类型、温度参数、创造性水平
- **批量处理**: 并发任务数、批处理大小、重试设置
- **性能优化**: 缓存设置、预加载、懒加载

## 使用方法

### 访问设置页面
1. 启动AIHawk应用
2. 在左侧导航栏点击"设置"
3. 选择相应的设置选项卡

### 设置保存
- **自动保存**: 默认启用，更改后自动保存到服务器
- **手动保存**: 点击右上角"保存设置"按钮
- **本地存储**: 设置同时保存到浏览器本地存储

### 备份与恢复
1. **创建备份**: 数据管理 → 点击"创建备份"
2. **恢复备份**: 备份列表 → 点击恢复图标
3. **删除备份**: 备份列表 → 点击删除图标

### 导出与导入
1. **导出设置**: 数据管理 → 点击"导出设置"
2. **导入设置**: 数据管理 → 点击"导入设置" → 选择文件

## API接口

### 基础接口
- `GET /api/settings/load` - 加载设置
- `POST /api/settings/save` - 保存设置
- `POST /api/settings/reset` - 重置设置

### 备份接口
- `POST /api/settings/backup` - 创建备份
- `GET /api/settings/backups` - 获取备份列表
- `POST /api/settings/restore/{filename}` - 恢复备份
- `DELETE /api/settings/backups/{filename}` - 删除备份

### 数据管理接口
- `POST /api/settings/export` - 导出设置
- `POST /api/settings/import` - 导入设置
- `DELETE /api/settings/clear-cache` - 清除缓存

## 配置文件

### 用户设置文件
- **位置**: `data_folder/user_settings.yaml`
- **格式**: YAML
- **备份**: 自动备份到 `data_folder/backups/`

### 默认设置
系统提供完整的默认设置，确保在没有用户配置时也能正常工作。

## 安全性

### 数据保护
- 重要操作前自动创建备份
- 设置验证和合并机制
- 错误恢复和回滚功能

### 权限控制
- 本地文件访问限制
- API接口安全验证
- 敏感信息保护

## 测试

### 后端测试
```bash
python test_settings.py
```

### 前端测试
1. 启动后端服务
2. 启动前端服务
3. 访问设置页面测试各功能

## 故障排除

### 常见问题

1. **设置无法保存**
   - 检查后端服务是否运行
   - 确认文件权限正确
   - 查看浏览器控制台错误

2. **备份功能异常**
   - 检查 `data_folder/backups/` 目录权限
   - 确认磁盘空间充足

3. **导入设置失败**
   - 验证文件格式正确（JSON/YAML）
   - 检查文件内容结构

### 日志查看
- 后端日志: 控制台输出
- 前端日志: 浏览器开发者工具

## 开发说明

### 添加新设置项
1. 在 `SettingsContext.jsx` 中添加默认值
2. 在对应设置组件中添加UI控件
3. 在 `settings_api.py` 中更新默认设置

### 自定义设置组件
1. 创建新的设置组件文件
2. 在 `Settings.jsx` 中注册新选项卡
3. 实现设置的读取和保存逻辑

## 注意事项

1. **兼容性**: 确保新设置不破坏现有功能
2. **性能**: 避免频繁的设置保存操作
3. **用户体验**: 提供清晰的设置说明和反馈
4. **数据安全**: 重要操作前提醒用户并创建备份

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的设置管理系统
- LinkedIn自动化设置
- 数据管理功能
- 系统设置选项
- 高级功能配置
