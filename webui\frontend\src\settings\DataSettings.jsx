import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Storage as StorageIcon,
  Backup as BackupIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Restore as RestoreIcon,
  Clear as ClearIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useSettings } from '../SettingsContext';

function DataSettings({ onReset }) {
  const { settings, updateSettings, getSetting } = useSettings();
  const [backups, setBackups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({ open: false, action: '', data: null });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  const handleChange = (path, value) => {
    updateSettings(path, value);
  };

  // 加载备份列表
  const loadBackups = async () => {
    try {
      const response = await fetch('http://localhost:8003/api/settings/backups');
      if (response.ok) {
        const backupList = await response.json();
        setBackups(backupList);
      }
    } catch (error) {
      console.error('加载备份列表失败:', error);
    }
  };

  useEffect(() => {
    loadBackups();
  }, []);

  // 创建备份
  const createBackup = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8003/api/settings/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: '手动备份' })
      });
      
      if (response.ok) {
        setSnackbar({ open: true, message: '备份创建成功！', severity: 'success' });
        loadBackups();
      } else {
        throw new Error('备份创建失败');
      }
    } catch (error) {
      setSnackbar({ open: true, message: `备份失败: ${error.message}`, severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 恢复备份
  const restoreBackup = async (filename) => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8003/api/settings/restore/${filename}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setSnackbar({ open: true, message: '备份恢复成功！', severity: 'success' });
        // 刷新页面以应用新设置
        setTimeout(() => window.location.reload(), 1000);
      } else {
        throw new Error('备份恢复失败');
      }
    } catch (error) {
      setSnackbar({ open: true, message: `恢复失败: ${error.message}`, severity: 'error' });
    } finally {
      setLoading(false);
      setConfirmDialog({ open: false, action: '', data: null });
    }
  };

  // 删除备份
  const deleteBackup = async (filename) => {
    try {
      const response = await fetch(`http://localhost:8003/api/settings/backups/${filename}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setSnackbar({ open: true, message: '备份删除成功！', severity: 'success' });
        loadBackups();
      } else {
        throw new Error('备份删除失败');
      }
    } catch (error) {
      setSnackbar({ open: true, message: `删除失败: ${error.message}`, severity: 'error' });
    } finally {
      setConfirmDialog({ open: false, action: '', data: null });
    }
  };

  // 导出数据
  const exportData = () => {
    const dataToExport = {
      settings: settings,
      exportTime: new Date().toISOString(),
      version: '1.0'
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `aihawk-settings-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    setSnackbar({ open: true, message: '设置导出成功！', severity: 'success' });
  };

  // 导入数据
  const importData = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target.result);
        if (importedData.settings) {
          // 这里应该调用设置更新函数
          setSnackbar({ open: true, message: '设置导入成功！', severity: 'success' });
        } else {
          throw new Error('无效的设置文件格式');
        }
      } catch (error) {
        setSnackbar({ open: true, message: `导入失败: ${error.message}`, severity: 'error' });
      }
    };
    reader.readAsText(file);
  };

  // 清除历史记录
  const clearHistory = () => {
    localStorage.removeItem('resumeJobUrl');
    localStorage.removeItem('resumeResult');
    localStorage.removeItem('uploadedResume');
    localStorage.removeItem('optimizedResumeHtml');
    localStorage.removeItem('jobInfo');
    localStorage.removeItem('coverLetterHtml');
    localStorage.removeItem('linkedinSearchResults');
    localStorage.removeItem('linkedinSelectedJobs');
    
    setSnackbar({ open: true, message: '历史记录清除成功！', severity: 'success' });
    setConfirmDialog({ open: false, action: '', data: null });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <StorageIcon sx={{ mr: 2, color: 'primary.main' }} />
        数据管理
      </Typography>

      {/* 备份设置 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <BackupIcon sx={{ mr: 1, color: 'secondary.main' }} />
              备份设置
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => onReset('data')}
              sx={{ color: 'text.secondary' }}
            >
              重置此部分
            </Button>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={getSetting('data.auto_backup') || true}
                    onChange={(e) => handleChange('data.auto_backup', e.target.checked)}
                  />
                }
                label="自动备份"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                在重要操作前自动创建备份
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>备份频率</InputLabel>
                <Select
                  value={getSetting('data.backup_frequency') || 'daily'}
                  onChange={(e) => handleChange('data.backup_frequency', e.target.value)}
                  label="备份频率"
                >
                  <MenuItem value="daily">每日</MenuItem>
                  <MenuItem value="weekly">每周</MenuItem>
                  <MenuItem value="monthly">每月</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="最大备份文件数"
                type="number"
                value={getSetting('data.max_backup_files') || 10}
                onChange={(e) => handleChange('data.max_backup_files', parseInt(e.target.value))}
                inputProps={{ min: 1, max: 50 }}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth size="small">
                <InputLabel>导出格式</InputLabel>
                <Select
                  value={getSetting('data.export_format') || 'json'}
                  onChange={(e) => handleChange('data.export_format', e.target.value)}
                  label="导出格式"
                >
                  <MenuItem value="json">JSON</MenuItem>
                  <MenuItem value="yaml">YAML</MenuItem>
                  <MenuItem value="csv">CSV</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<BackupIcon />}
              onClick={createBackup}
              disabled={loading}
            >
              创建备份
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={exportData}
            >
              导出设置
            </Button>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
            >
              导入设置
              <input
                type="file"
                hidden
                accept=".json"
                onChange={importData}
              />
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* 备份列表 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>备份文件</Typography>
          
          {loading && <LinearProgress sx={{ mb: 2 }} />}
          
          {backups.length === 0 ? (
            <Alert severity="info">暂无备份文件</Alert>
          ) : (
            <List>
              {backups.map((backup, index) => (
                <ListItem key={index} divider>
                  <ListItemText
                    primary={backup.filename}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          创建时间: {formatDate(backup.created_at)}
                        </Typography>
                        <Typography variant="caption" display="block">
                          文件大小: {formatFileSize(backup.size)}
                        </Typography>
                        {backup.description && (
                          <Typography variant="caption" display="block">
                            描述: {backup.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => setConfirmDialog({
                        open: true,
                        action: 'restore',
                        data: backup.filename
                      })}
                      sx={{ mr: 1 }}
                    >
                      <RestoreIcon />
                    </IconButton>
                    <IconButton
                      edge="end"
                      onClick={() => setConfirmDialog({
                        open: true,
                        action: 'delete',
                        data: backup.filename
                      })}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* 历史记录管理 */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <HistoryIcon sx={{ mr: 1, color: 'secondary.main' }} />
            历史记录管理
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="自动清理天数"
                type="number"
                value={getSetting('data.clear_history_days') || 30}
                onChange={(e) => handleChange('data.clear_history_days', parseInt(e.target.value))}
                inputProps={{ min: 1, max: 365 }}
                size="small"
                helperText="超过指定天数的历史记录将被自动清理"
              />
            </Grid>

            <Grid item xs={12}>
              <Button
                variant="outlined"
                color="warning"
                startIcon={<ClearIcon />}
                onClick={() => setConfirmDialog({
                  open: true,
                  action: 'clearHistory',
                  data: null
                })}
              >
                清除所有历史记录
              </Button>
              <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 1 }}>
                包括简历生成记录、求职信记录、LinkedIn搜索记录等
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 确认对话框 */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, action: '', data: null })}
      >
        <DialogTitle>
          {confirmDialog.action === 'restore' && '确认恢复备份'}
          {confirmDialog.action === 'delete' && '确认删除备份'}
          {confirmDialog.action === 'clearHistory' && '确认清除历史记录'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {confirmDialog.action === 'restore' && `确定要恢复备份 "${confirmDialog.data}" 吗？当前设置将被覆盖。`}
            {confirmDialog.action === 'delete' && `确定要删除备份 "${confirmDialog.data}" 吗？此操作不可撤销。`}
            {confirmDialog.action === 'clearHistory' && '确定要清除所有历史记录吗？此操作不可撤销。'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog({ open: false, action: '', data: null })}>
            取消
          </Button>
          <Button
            onClick={() => {
              if (confirmDialog.action === 'restore') {
                restoreBackup(confirmDialog.data);
              } else if (confirmDialog.action === 'delete') {
                deleteBackup(confirmDialog.data);
              } else if (confirmDialog.action === 'clearHistory') {
                clearHistory();
              }
            }}
            color="primary"
            variant="contained"
          >
            确认
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default DataSettings;
