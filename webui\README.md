# WebUI 目录结构与开发说明

本目录用于存放 Web UI 相关代码，实现与命令行工具解耦的前后端分离架构。

## 目录结构建议

```
webui/
├── backend/    # FastAPI 后端接口
│   └── ...
├── frontend/   # React/Vue 前端页面
│   └── ...
└── README.md   # 说明文档
```

## 开发流程
1. 后端（推荐 FastAPI）负责将原有 Python 逻辑封装为 RESTful API。
2. 前端（推荐 React 或 Vue）通过 HTTP 请求调用后端 API，实现页面交互。
3. 前后端通过 JSON 数据通信，互不影响。

## API 规划建议
- /api/resume/generate  生成定制简历
- /api/coverletter/generate  生成定制求职信
- /api/job/parse  解析职位描述

## 启动方式
- 后端：`cd backend && uvicorn main:app --reload`
- 前端：`cd frontend && npm install && npm run dev`

## 说明
- 该目录初始为空，后续将逐步补充后端和前端代码。
- 如需自定义 API 或页面，请在对应子目录下开发。