# AIHawk LinkedIn 自动化安装指南

## 概述

本项目支持两种浏览器自动化方案：
- **Playwright (推荐)** - 更现代、更稳定、更快速
- **Selenium (可选)** - 传统方案，兼容性好

**默认使用 Playwright**，这是推荐的选择。

## 快速安装

### 1. 安装基础依赖

```bash
pip install -r requirements.txt
```

### 2. 安装 Playwright 浏览器 (推荐)

运行我们提供的安装脚本：

```bash
python setup_playwright.py
```

或者手动安装：

```bash
pip install playwright>=1.40.0
python -m playwright install chromium
```

### 3. 验证安装

启动 Web UI 测试：

```bash
cd webui/backend
python main.py
```

然后在浏览器中访问 `http://localhost:8000`

## 详细安装步骤

### 系统要求

- Python 3.8 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux
- 至少 2GB 可用磁盘空间

### 依赖说明

#### 核心依赖 (requirements.txt)
- `selenium==4.9.1` - Selenium 浏览器自动化
- `playwright>=1.40.0` - Playwright 浏览器自动化 (推荐)
- `beautifulsoup4>=4.12.0` - HTML 解析
- `loguru==0.7.2` - 日志记录
- `fastapi`, `uvicorn` - Web API 框架
- 其他支持库...

#### Playwright 浏览器
Playwright 需要下载浏览器二进制文件：
```bash
python -m playwright install chromium
```

## 使用指南

### 1. 启动应用

```bash
cd webui/backend
python main.py
```

### 2. 访问 Web 界面

打开浏览器访问：`http://localhost:8000`

### 3. 选择自动化类型

在登录对话框中，您可以选择：
- **Playwright (推荐)** - 默认选项，更稳定快速
- **Selenium (可选)** - 备用选项

### 4. 配置登录信息

- 输入您的 LinkedIn 邮箱和密码
- 选择是否使用无头模式（建议关闭以处理验证码）
- 点击登录

## 故障排除

### Playwright 安装问题

如果 Playwright 安装失败：

1. **权限问题**：
   ```bash
   # Windows (以管理员身份运行)
   python -m playwright install chromium
   
   # Linux/macOS
   sudo python -m playwright install chromium
   ```

2. **网络问题**：
   ```bash
   # 使用代理
   HTTPS_PROXY=http://proxy:port python -m playwright install chromium
   ```

3. **手动下载**：
   访问 [Playwright 官方文档](https://playwright.dev/python/docs/browsers) 获取手动安装指南

### Selenium 问题

如果选择使用 Selenium：

1. **ChromeDriver 问题**：
   项目使用 `webdriver-manager` 自动管理 ChromeDriver，通常无需手动安装

2. **Chrome 浏览器**：
   确保系统已安装 Google Chrome 浏览器

### 常见错误

1. **模块导入错误**：
   ```bash
   pip install --upgrade -r requirements.txt
   ```

2. **端口占用**：
   ```bash
   # 更改端口
   uvicorn main:app --host 0.0.0.0 --port 8001
   ```

3. **权限错误**：
   确保有足够权限访问浏览器和网络

## 高级配置

### 配置文件

编辑 `linkedin_config.yaml` 自定义设置：

```yaml
linkedin:
  search_keywords: ['python developer', 'software engineer']
  location: 'United States'
  max_applications_per_day: 50

playwright:
  headless: false
  timeout: 30000
  window_size: [1200, 800]
```

### 环境变量

```bash
# 设置日志级别
export LOG_LEVEL=DEBUG

# 设置代理
export HTTPS_PROXY=http://proxy:port
```

## 支持

如果遇到问题：

1. 查看日志文件：`log/linkedin_automation.log`
2. 运行诊断脚本：`python setup_playwright.py`
3. 检查依赖：`pip list | grep -E "(playwright|selenium|beautifulsoup4)"`

## 更新

保持依赖最新：

```bash
pip install --upgrade -r requirements.txt
python -m playwright install chromium
```
